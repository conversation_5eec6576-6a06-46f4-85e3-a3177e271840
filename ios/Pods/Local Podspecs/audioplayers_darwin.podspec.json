{"name": "audioplayers_darwin", "version": "0.0.1", "summary": "iOS implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously.", "description": "iOS implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously.", "homepage": "https://github.com/bluefireteam/audioplayers", "license": {"file": "../LICENSE"}, "authors": {"Blue Fire": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"Flutter": []}, "platforms": {"ios": "9.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "swift_versions": "5.0", "swift_version": "5.0"}