ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DKImagePickerController" "${PODS_CONFIGURATION_BUILD_DIR}/DKPhotoGallery" "${PODS_CONFIGURATION_BUILD_DIR}/OrderedSet" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyGif" "${PODS_CONFIGURATION_BUILD_DIR}/audio_service" "${PODS_CONFIGURATION_BUILD_DIR}/audio_session" "${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/battery_plus" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/file_picker" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_file_dialog" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_inappwebview_ios" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_tts" "${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "${PODS_CONFIGURATION_BUILD_DIR}/in_app_purchase_storekit" "${PODS_CONFIGURATION_BUILD_DIR}/just_audio" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "${PODS_CONFIGURATION_BUILD_DIR}/pointer_interceptor_ios" "${PODS_CONFIGURATION_BUILD_DIR}/receive_sharing_intent" "${PODS_CONFIGURATION_BUILD_DIR}/saver_gallery" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DKImagePickerController/DKImagePickerController.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/DKPhotoGallery/DKPhotoGallery.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/OrderedSet/OrderedSet.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyGif/SwiftyGif.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/audio_service/audio_service.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/audio_session/audio_session.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin/audioplayers_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/battery_plus/battery_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/file_picker/file_picker.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_file_dialog/flutter_file_dialog.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_tts/flutter_tts.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast/fluttertoast.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/in_app_purchase_storekit/in_app_purchase_storekit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/just_audio/just_audio.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/pointer_interceptor_ios/pointer_interceptor_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/receive_sharing_intent/receive_sharing_intent.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/saver_gallery/saver_gallery.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus/share_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin/sqflite_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -l"swiftCoreGraphics" -framework "AVFoundation" -framework "AVKit" -framework "DKImagePickerController" -framework "DKPhotoGallery" -framework "Foundation" -framework "ImageIO" -framework "OrderedSet" -framework "Photos" -framework "SDWebImage" -framework "SwiftyGif" -framework "UIKit" -framework "audio_service" -framework "audio_session" -framework "audioplayers_darwin" -framework "battery_plus" -framework "connectivity_plus" -framework "device_info_plus" -framework "file_picker" -framework "flutter_file_dialog" -framework "flutter_inappwebview_ios" -framework "flutter_tts" -framework "fluttertoast" -framework "in_app_purchase_storekit" -framework "just_audio" -framework "package_info_plus" -framework "path_provider_foundation" -framework "permission_handler_apple" -framework "pointer_interceptor_ios" -framework "receive_sharing_intent" -framework "saver_gallery" -framework "share_plus" -framework "shared_preferences_foundation" -framework "sqflite_darwin" -framework "url_launcher_ios" -framework "wakelock_plus" -weak_framework "LinkPresentation"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/DKImagePickerController" "-F${PODS_CONFIGURATION_BUILD_DIR}/DKPhotoGallery" "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/OrderedSet" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftyGif" "-F${PODS_CONFIGURATION_BUILD_DIR}/audio_service" "-F${PODS_CONFIGURATION_BUILD_DIR}/audio_session" "-F${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/battery_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/file_picker" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_file_dialog" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_inappwebview_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_tts" "-F${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "-F${PODS_CONFIGURATION_BUILD_DIR}/in_app_purchase_storekit" "-F${PODS_CONFIGURATION_BUILD_DIR}/just_audio" "-F${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/pointer_interceptor_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/receive_sharing_intent" "-F${PODS_CONFIGURATION_BUILD_DIR}/saver_gallery" "-F${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
