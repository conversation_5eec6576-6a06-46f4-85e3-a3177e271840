import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/utils/platform_validation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';

/// Interactive platform validation page for testing iOS/Android UI components
class PlatformValidationPage extends StatefulWidget {
  const PlatformValidationPage({super.key});

  @override
  State<PlatformValidationPage> createState() => _PlatformValidationPageState();
}

class _PlatformValidationPageState extends State<PlatformValidationPage> {
  ValidationResult? _validationResult;
  bool _isValidating = false;

  @override
  void initState() {
    super.initState();
    _runValidation();
  }

  Future<void> _runValidation() async {
    setState(() {
      _isValidating = true;
    });

    try {
      final result = await PlatformValidation.validatePlatformUI(context);
      setState(() {
        _validationResult = result;
      });
      
      AnxLog.info('Platform validation completed: ${result.overallSuccess ? "SUCCESS" : "FAILED"}');
      AnxToast.show(result.overallSuccess ? 'Validation passed!' : 'Validation failed!');
    } catch (e) {
      AnxLog.severe('Validation error: $e');
      AnxToast.show('Validation error: $e');
    } finally {
      setState(() {
        _isValidating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AdaptivePage(
      title: Text('Platform UI Validation'),
      body: _isValidating
          ? const Center(child: CircularProgressIndicator())
          : _buildValidationContent(),
    );
  }

  Widget _buildValidationContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPlatformInfo(),
          const SizedBox(height: DesignSystem.spaceL),
          _buildValidationResults(),
          const SizedBox(height: DesignSystem.spaceL),
          _buildInteractiveTests(),
          const SizedBox(height: DesignSystem.spaceL),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPlatformInfo() {
    return AdaptiveCard(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Platform Information',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: DesignSystem.spaceS),
            _buildInfoRow('Platform', PlatformAdaptations.platformName),
            _buildInfoRow('Is iOS', PlatformAdaptations.isIOS.toString()),
            _buildInfoRow('Is Android', PlatformAdaptations.isAndroid.toString()),
            _buildInfoRow('Is Mobile', PlatformAdaptations.isMobile.toString()),
            _buildInfoRow('Is Desktop', PlatformAdaptations.isDesktop.toString()),
            const SizedBox(height: DesignSystem.spaceS),
            Text(
              'Adaptive Constants',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: DesignSystem.spaceXS),
            _buildInfoRow('Border Radius', '${PlatformAdaptations.adaptiveBorderRadius}px'),
            _buildInfoRow('Button Height', '${PlatformAdaptations.adaptiveButtonHeight}px'),
            _buildInfoRow('App Bar Height', '${PlatformAdaptations.adaptiveAppBarHeight}px'),
            _buildInfoRow('Elevation', '${PlatformAdaptations.adaptiveElevation}px'),
            _buildInfoRow('Haptic Feedback', PlatformAdaptations.shouldUseHapticFeedback.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceXS / 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: Theme.of(context).textTheme.bodyMedium),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationResults() {
    if (_validationResult == null) {
      return const SizedBox.shrink();
    }

    return AdaptiveCard(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _validationResult!.overallSuccess ? Icons.check_circle : Icons.error,
                  color: _validationResult!.overallSuccess ? Colors.green : Colors.red,
                ),
                const SizedBox(width: DesignSystem.spaceS),
                Text(
                  'Validation Results',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: DesignSystem.spaceS),
            ..._validationResult!.results.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceXS / 2),
                child: Row(
                  children: [
                    Icon(
                      entry.value ? Icons.check : Icons.close,
                      color: entry.value ? Colors.green : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: DesignSystem.spaceS),
                    Expanded(child: Text(entry.key)),
                  ],
                ),
              );
            }),
            if (_validationResult!.errors.isNotEmpty) ...[
              const SizedBox(height: DesignSystem.spaceS),
              Text(
                'Errors:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red,
                ),
              ),
              ..._validationResult!.errors.map((error) {
                return Padding(
                  padding: const EdgeInsets.only(left: DesignSystem.spaceM, top: DesignSystem.spaceXS),
                  child: Text(
                    error,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                    ),
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInteractiveTests() {
    return AdaptiveCard(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Interactive Tests',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: DesignSystem.spaceS),
            
            // Test adaptive icons
            _buildTestSection(
              'Adaptive Icons',
              Wrap(
                spacing: DesignSystem.spaceS,
                children: [
                  _buildIconTest('Back', AdaptiveIcons.back),
                  _buildIconTest('Forward', AdaptiveIcons.forward),
                  _buildIconTest('Settings', AdaptiveIcons.settings),
                  _buildIconTest('Home', AdaptiveIcons.home),
                  _buildIconTest('Search', AdaptiveIcons.search),
                  _buildIconTest('Close', AdaptiveIcons.close),
                ],
              ),
            ),
            
            const SizedBox(height: DesignSystem.spaceM),
            
            // Test adaptive buttons
            _buildTestSection(
              'Adaptive Buttons',
              Column(
                children: [
                  AdaptiveButton(
                    onPressed: () => _testHapticFeedback(),
                    child: const Text('Test Haptic Feedback'),
                  ),
                  const SizedBox(height: DesignSystem.spaceS),
                  AdaptiveButton(
                    onPressed: () => _testDialog(),
                    child: const Text('Test Adaptive Dialog'),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: DesignSystem.spaceM),
            
            // Test adaptive list tiles
            _buildTestSection(
              'Adaptive List Tiles',
              Column(
                children: [
                  AdaptiveListTile(
                    leading: Icon(AdaptiveIcons.settings),
                    title: const Text('Settings'),
                    onTap: () => _testNavigation(),
                  ),
                  AdaptiveListTile(
                    leading: Icon(AdaptiveIcons.dictionary),
                    title: const Text('Dictionary'),
                    subtitle: const Text('Platform-adaptive list tile'),
                    onTap: () => _testNavigation(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: DesignSystem.spaceS),
        content,
      ],
    );
  }

  Widget _buildIconTest(String label, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24),
        const SizedBox(height: DesignSystem.spaceXS),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: AdaptiveButton(
            onPressed: _runValidation,
            child: const Text('Re-run Validation'),
          ),
        ),
        const SizedBox(width: DesignSystem.spaceS),
        Expanded(
          child: AdaptiveButton(
            onPressed: _copyReport,
            child: const Text('Copy Report'),
          ),
        ),
      ],
    );
  }

  void _testHapticFeedback() {
    if (PlatformAdaptations.shouldUseHapticFeedback) {
      HapticFeedback.lightImpact();
      AnxToast.show('Haptic feedback triggered');
    } else {
      AnxToast.show('Haptic feedback not available');
    }
  }

  void _testDialog() {
    AdaptiveDialogs.showAlert(
      context: context,
      title: 'Platform Test',
      content: 'This dialog uses platform-appropriate styling.',
      actions: [
        AdaptiveDialogAction(
          text: 'OK',
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  void _testNavigation() {
    AdaptiveNavigation.push(
      context,
      Scaffold(
        appBar: AdaptiveNavigation.createAdaptiveAppBar(
          context: context,
          title: const Text('Test Page'),
        ),
        body: const Center(
          child: Text('Platform-adaptive navigation test'),
        ),
      ),
    );
  }

  void _copyReport() {
    if (_validationResult != null) {
      Clipboard.setData(ClipboardData(text: _validationResult!.getReport()));
      AnxToast.show('Report copied to clipboard');
    }
  }
}
