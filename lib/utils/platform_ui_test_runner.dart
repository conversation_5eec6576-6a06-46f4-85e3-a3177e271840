import 'package:flutter/material.dart';
import 'package:dasso_reader/utils/platform_validation.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';

/// Test runner for platform-specific UI validation
///
/// This utility provides methods to run comprehensive platform UI tests
/// and generate detailed reports for iOS/Android compatibility validation.
class PlatformUITestRunner {
  PlatformUITestRunner._();

  /// Runs comprehensive platform UI validation tests
  static Future<void> runAllTests(BuildContext context) async {
    AnxLog.info('Starting comprehensive platform UI validation...');

    try {
      // Run platform validation
      final validationResult =
          await PlatformValidation.validatePlatformUI(context);

      // Log detailed results
      AnxLog.info('Platform UI Validation Results:');
      AnxLog.info('Overall Success: ${validationResult.overallSuccess}');

      validationResult.results.forEach((component, success) {
        final status = success ? '✅ PASS' : '❌ FAIL';
        AnxLog.info('  $status $component');
      });

      if (validationResult.errors.isNotEmpty) {
        AnxLog.warning('Validation Errors:');
        for (final error in validationResult.errors) {
          AnxLog.warning('  ❌ $error');
        }
      }

      // Show user-friendly toast
      final message = validationResult.overallSuccess
          ? 'All platform UI tests passed! ✅'
          : 'Some platform UI tests failed. Check logs for details. ❌';

      AnxToast.show(message);

      // Generate and log full report
      final report = validationResult.getReport();
      AnxLog.info('Full Validation Report:\n$report');
    } catch (e) {
      AnxLog.severe('Platform UI test runner failed: $e');
      AnxToast.show('Platform UI tests failed: $e');
    }
  }

  /// Runs quick validation check (subset of tests)
  static Future<bool> runQuickCheck(BuildContext context) async {
    try {
      AnxLog.info('Running quick platform UI check...');

      final validationResult =
          await PlatformValidation.validatePlatformUI(context);

      if (validationResult.overallSuccess) {
        AnxLog.info('Quick platform UI check passed ✅');
        return true;
      } else {
        AnxLog.warning('Quick platform UI check failed ❌');
        AnxLog.warning(
            'Failed components: ${validationResult.results.entries.where((e) => !e.value).map((e) => e.key).join(', ')}');
        return false;
      }
    } catch (e) {
      AnxLog.severe('Quick platform UI check error: $e');
      return false;
    }
  }

  /// Validates specific platform UI component
  static Future<bool> validateComponent(
    BuildContext context,
    String componentName,
  ) async {
    try {
      AnxLog.info('Validating component: $componentName');

      final validationResult =
          await PlatformValidation.validatePlatformUI(context);

      if (validationResult.results.containsKey(componentName)) {
        final success = validationResult.results[componentName]!;
        AnxLog.info('Component $componentName: ${success ? 'PASS' : 'FAIL'}');
        return success;
      } else {
        AnxLog.warning(
            'Component $componentName not found in validation results');
        return false;
      }
    } catch (e) {
      AnxLog.severe('Component validation error: $e');
      return false;
    }
  }

  /// Gets platform-specific recommendations
  static List<String> getPlatformRecommendations() {
    final recommendations = <String>[];

    if (PlatformAdaptations.isIOS) {
      recommendations.addAll([
        '🍎 iOS Platform Recommendations:',
        '• Use CupertinoPageRoute for navigation',
        '• Implement flat design with zero elevation',
        '• Use BouncingScrollPhysics for scroll views',
        '• Center app bar titles',
        '• Use Cupertino icons for native feel',
        '• Implement haptic feedback for interactions',
        '• Use 12px border radius for rounded corners',
        '• Avoid Material shadows, use subtle iOS-style shadows',
      ]);
    } else {
      recommendations.addAll([
        '🤖 Android Platform Recommendations:',
        '• Use MaterialPageRoute for navigation',
        '• Implement Material Design elevation',
        '• Use ClampingScrollPhysics for scroll views',
        '• Left-align app bar titles',
        '• Use Material icons for consistency',
        '• Follow Material 3 design guidelines',
        '• Use 8px border radius for Material components',
        '• Implement proper Material shadows and elevation',
      ]);
    }

    return recommendations;
  }

  /// Generates platform-specific UI checklist
  static Map<String, List<String>> generateUIChecklist() {
    return {
      'Navigation': [
        'Use AdaptiveNavigation.push() instead of Navigator.push()',
        'Implement platform-appropriate page transitions',
        'Use adaptive app bars with proper styling',
        'Ensure back button uses platform-appropriate icons',
      ],
      'Icons': [
        'Use AdaptiveIcons for platform-appropriate icons',
        'Ensure icons follow platform design guidelines',
        'Provide proper tooltips for accessibility',
        'Use consistent icon sizes across the app',
      ],
      'Components': [
        'Use AdaptiveButton for platform-appropriate styling',
        'Implement AdaptiveListTile for consistent list items',
        'Use AdaptiveCard for platform-appropriate containers',
        'Ensure proper touch target sizes (44dp minimum)',
      ],
      'Styling': [
        'Use platform-appropriate border radius',
        'Implement correct elevation for each platform',
        'Use platform-appropriate scroll physics',
        'Follow platform color and typography guidelines',
      ],
      'Interactions': [
        'Implement haptic feedback on iOS',
        'Use platform-appropriate dialog styles',
        'Ensure proper focus and selection states',
        'Follow platform-specific interaction patterns',
      ],
    };
  }

  /// Logs platform-specific UI guidelines
  static void logPlatformGuidelines() {
    AnxLog.info('=== Platform UI Guidelines ===');

    final recommendations = getPlatformRecommendations();
    for (final recommendation in recommendations) {
      AnxLog.info(recommendation);
    }

    AnxLog.info('\n=== UI Component Checklist ===');
    final checklist = generateUIChecklist();
    checklist.forEach((category, items) {
      AnxLog.info('$category:');
      for (final item in items) {
        AnxLog.info('  • $item');
      }
    });
  }
}
