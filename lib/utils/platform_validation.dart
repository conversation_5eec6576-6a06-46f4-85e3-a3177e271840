import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Platform-specific UI validation utilities for iOS/Android compatibility
///
/// This class provides comprehensive validation of platform-adaptive components
/// to ensure proper behavior across iOS and Android platforms.
class PlatformValidation {
  PlatformValidation._();

  /// Validates all platform-specific UI components
  static Future<ValidationResult> validatePlatformUI(
      BuildContext context) async {
    final results = <String, bool>{};
    final errors = <String>[];

    try {
      // Test platform detection
      results['Platform Detection'] = _validatePlatformDetection();

      // Test adaptive icons
      results['Adaptive Icons'] = _validateAdaptiveIcons();

      // Test platform adaptations
      results['Platform Adaptations'] = _validatePlatformAdaptations();

      // Test navigation components
      results['Navigation Components'] =
          await _validateNavigationComponents(context);

      // Test UI constants
      results['UI Constants'] = _validateUIConstants();

      // Test scroll physics
      results['Scroll Physics'] = _validateScrollPhysics();

      // Test haptic feedback
      results['Haptic Feedback'] = await _validateHapticFeedback();

      // Test status bar handling
      results['Status Bar'] = _validateStatusBarHandling();

      // Test Material Design vs Cupertino compliance
      results['Platform UI Compliance'] = _validatePlatformUICompliance();

      // Test adaptive component consistency
      results['Adaptive Components'] =
          await _validateAdaptiveComponents(context);
    } catch (e) {
      errors.add('Validation error: $e');
      AnxLog.severe('Platform validation failed: $e');
    }

    return ValidationResult(
      results: results,
      errors: errors,
      overallSuccess:
          errors.isEmpty && results.values.every((result) => result),
    );
  }

  /// Validates platform detection functionality
  static bool _validatePlatformDetection() {
    try {
      // Test basic platform detection
      final isIOS = PlatformAdaptations.isIOS;
      final isAndroid = PlatformAdaptations.isAndroid;
      final isMobile = PlatformAdaptations.isMobile;
      final isDesktop = PlatformAdaptations.isDesktop;

      // Ensure exactly one platform is detected
      final platformCount =
          [isIOS, isAndroid, isDesktop].where((p) => p).length;
      if (platformCount != 1) {
        AnxLog.warning(
            'Multiple or no platforms detected: iOS=$isIOS, Android=$isAndroid, Desktop=$isDesktop');
        return false;
      }

      // Validate mobile detection
      if (isMobile != (isIOS || isAndroid)) {
        AnxLog.warning(
            'Mobile detection inconsistent: isMobile=$isMobile, isIOS=$isIOS, isAndroid=$isAndroid');
        return false;
      }

      // Test platform name
      final platformName = PlatformAdaptations.platformName;
      if (platformName.isEmpty) {
        AnxLog.warning('Platform name is empty');
        return false;
      }

      AnxLog.info('Platform detection validated: $platformName');
      return true;
    } catch (e) {
      AnxLog.severe('Platform detection validation failed: $e');
      return false;
    }
  }

  /// Validates adaptive icons functionality
  static bool _validateAdaptiveIcons() {
    try {
      // Test key adaptive icons
      final icons = [
        AdaptiveIcons.back,
        AdaptiveIcons.forward,
        AdaptiveIcons.settings,
        AdaptiveIcons.home,
        AdaptiveIcons.search,
        AdaptiveIcons.close,
        AdaptiveIcons.chevronRight,
        AdaptiveIcons.dictionary,
        AdaptiveIcons.vocabulary,
        AdaptiveIcons.hsk,
      ];

      // Ensure all icons are valid
      for (final icon in icons) {
        if (icon.codePoint == 0) {
          AnxLog.warning('Invalid icon detected: codePoint is 0');
          return false;
        }
      }

      // Test platform-specific icon selection
      if (PlatformAdaptations.isIOS) {
        // On iOS, back icon should be CupertinoIcons.back
        if (AdaptiveIcons.back != CupertinoIcons.back) {
          AnxLog.warning('iOS back icon not using CupertinoIcons.back');
          return false;
        }
      } else {
        // On Android, back icon should be Icons.arrow_back
        if (AdaptiveIcons.back != Icons.arrow_back) {
          AnxLog.warning('Android back icon not using Icons.arrow_back');
          return false;
        }
      }

      AnxLog.info('Adaptive icons validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Adaptive icons validation failed: $e');
      return false;
    }
  }

  /// Validates platform adaptations constants
  static bool _validatePlatformAdaptations() {
    try {
      // Test adaptive constants
      final borderRadius = PlatformAdaptations.adaptiveBorderRadius;
      final buttonHeight = PlatformAdaptations.adaptiveButtonHeight;
      final listItemHeight = PlatformAdaptations.adaptiveListItemHeight;
      final appBarHeight = PlatformAdaptations.adaptiveAppBarHeight;
      final elevation = PlatformAdaptations.adaptiveElevation;
      // Test shadow blur value
      final shadowBlur = PlatformAdaptations.adaptiveShadowBlur;
      if (shadowBlur < 0 || shadowBlur > 20) {
        AnxLog.warning('Invalid shadow blur: $shadowBlur');
        return false;
      }

      // Validate reasonable values
      if (borderRadius < 0 || borderRadius > 20) {
        AnxLog.warning('Invalid border radius: $borderRadius');
        return false;
      }

      if (buttonHeight < 40 || buttonHeight > 60) {
        AnxLog.warning('Invalid button height: $buttonHeight');
        return false;
      }

      if (listItemHeight < 40 || listItemHeight > 80) {
        AnxLog.warning('Invalid list item height: $listItemHeight');
        return false;
      }

      if (appBarHeight < 40 || appBarHeight > 80) {
        AnxLog.warning('Invalid app bar height: $appBarHeight');
        return false;
      }

      if (elevation < 0 || elevation > 10) {
        AnxLog.warning('Invalid elevation: $elevation');
        return false;
      }

      // Test platform-specific values
      if (PlatformAdaptations.isIOS) {
        if (elevation != 0) {
          AnxLog.warning('iOS should have zero elevation, got: $elevation');
          return false;
        }
        if (borderRadius != 12.0) {
          AnxLog.warning(
              'iOS should have 12.0 border radius, got: $borderRadius');
          return false;
        }
      }

      AnxLog.info('Platform adaptations validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Platform adaptations validation failed: $e');
      return false;
    }
  }

  /// Validates navigation components
  static Future<bool> _validateNavigationComponents(
      BuildContext context) async {
    try {
      // Test page route creation
      final testWidget = Container();
      final pageRoute =
          PlatformAdaptations.createPageRoute<void>(page: testWidget);

      if (PlatformAdaptations.isIOS) {
        if (pageRoute is! CupertinoPageRoute) {
          AnxLog.warning('iOS should use CupertinoPageRoute');
          return false;
        }
      } else {
        if (pageRoute is! MaterialPageRoute) {
          AnxLog.warning('Android should use MaterialPageRoute');
          return false;
        }
      }

      // Test modal route creation
      final modalRoute =
          PlatformAdaptations.createModalRoute<void>(page: testWidget);
      if (modalRoute.fullscreenDialog != true) {
        AnxLog.warning('Modal route should be fullscreen dialog');
        return false;
      }

      AnxLog.info('Navigation components validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Navigation components validation failed: $e');
      return false;
    }
  }

  /// Validates UI constants
  static bool _validateUIConstants() {
    try {
      // Test DesignSystem constants
      final spaceValues = [
        DesignSystem.spaceXS,
        DesignSystem.spaceS,
        DesignSystem.spaceM,
        DesignSystem.spaceL,
        DesignSystem.spaceXL,
      ];

      // Ensure progressive spacing
      for (int i = 1; i < spaceValues.length; i++) {
        if (spaceValues[i] <= spaceValues[i - 1]) {
          AnxLog.warning(
              'Space values not progressive: ${spaceValues[i - 1]} -> ${spaceValues[i]}');
          return false;
        }
      }

      // Test radius values
      final radiusValues = [
        DesignSystem.radiusS,
        DesignSystem.radiusM,
        DesignSystem.radiusL,
      ];

      for (int i = 1; i < radiusValues.length; i++) {
        if (radiusValues[i] <= radiusValues[i - 1]) {
          AnxLog.warning(
              'Radius values not progressive: ${radiusValues[i - 1]} -> ${radiusValues[i]}');
          return false;
        }
      }

      AnxLog.info('UI constants validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('UI constants validation failed: $e');
      return false;
    }
  }

  /// Validates scroll physics
  static bool _validateScrollPhysics() {
    try {
      final scrollPhysics = PlatformAdaptations.adaptiveScrollPhysics;

      if (PlatformAdaptations.isIOS) {
        if (scrollPhysics is! BouncingScrollPhysics) {
          AnxLog.warning('iOS should use BouncingScrollPhysics');
          return false;
        }
      } else {
        if (scrollPhysics is! ClampingScrollPhysics) {
          AnxLog.warning('Android should use ClampingScrollPhysics');
          return false;
        }
      }

      AnxLog.info('Scroll physics validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Scroll physics validation failed: $e');
      return false;
    }
  }

  /// Validates haptic feedback
  static Future<bool> _validateHapticFeedback() async {
    try {
      final shouldUseHaptic = PlatformAdaptations.shouldUseHapticFeedback;

      if (PlatformAdaptations.isIOS && !shouldUseHaptic) {
        AnxLog.warning('iOS should use haptic feedback');
        return false;
      }

      // Test haptic feedback execution (won't actually trigger on simulator)
      if (shouldUseHaptic) {
        await HapticFeedback.lightImpact();
        await HapticFeedback.mediumImpact();
        await HapticFeedback.heavyImpact();
        await HapticFeedback.selectionClick();
      }

      AnxLog.info('Haptic feedback validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Haptic feedback validation failed: $e');
      return false;
    }
  }

  /// Validates status bar handling
  static bool _validateStatusBarHandling() {
    try {
      // Test status bar style calculation
      final lightBackground = Colors.white;
      final darkBackground = Colors.black;

      final lightLuminance = lightBackground.computeLuminance();
      final darkLuminance = darkBackground.computeLuminance();

      if (lightLuminance <= darkLuminance) {
        AnxLog.warning('Light background luminance calculation incorrect');
        return false;
      }

      AnxLog.info('Status bar handling validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Status bar handling validation failed: $e');
      return false;
    }
  }

  /// Validates platform UI compliance (Material Design vs Cupertino)
  static bool _validatePlatformUICompliance() {
    try {
      if (PlatformAdaptations.isIOS) {
        // iOS should use Cupertino design patterns
        // Check for flat design (no elevation)
        if (PlatformAdaptations.adaptiveElevation != 0) {
          AnxLog.warning('iOS should use flat design with zero elevation');
          return false;
        }

        // Check for iOS-appropriate border radius
        if (PlatformAdaptations.adaptiveBorderRadius != 12.0) {
          AnxLog.warning('iOS should use 12.0 border radius');
          return false;
        }

        // Check for bouncing scroll physics
        final scrollPhysics = PlatformAdaptations.adaptiveScrollPhysics;
        if (scrollPhysics is! BouncingScrollPhysics) {
          AnxLog.warning('iOS should use BouncingScrollPhysics');
          return false;
        }
      } else {
        // Android should use Material Design patterns
        // Check for Material elevation
        if (PlatformAdaptations.adaptiveElevation <= 0) {
          AnxLog.warning('Android should use Material elevation');
          return false;
        }

        // Check for Material border radius
        if (PlatformAdaptations.adaptiveBorderRadius != DesignSystem.radiusM) {
          AnxLog.warning('Android should use Material Design border radius');
          return false;
        }

        // Check for clamping scroll physics
        final scrollPhysics = PlatformAdaptations.adaptiveScrollPhysics;
        if (scrollPhysics is! ClampingScrollPhysics) {
          AnxLog.warning('Android should use ClampingScrollPhysics');
          return false;
        }
      }

      AnxLog.info('Platform UI compliance validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Platform UI compliance validation failed: $e');
      return false;
    }
  }

  /// Validates adaptive components consistency
  static Future<bool> _validateAdaptiveComponents(BuildContext context) async {
    try {
      // Test that adaptive components exist and are properly configured

      // Test adaptive button style
      final buttonStyle = PlatformAdaptations.getAdaptiveButtonStyle(context);
      if (buttonStyle.elevation == null) {
        AnxLog.warning('Adaptive button style missing elevation configuration');
        return false;
      }

      // Test adaptive card decoration
      final cardDecoration =
          PlatformAdaptations.getAdaptiveCardDecoration(context);
      if (cardDecoration.borderRadius == null) {
        AnxLog.warning('Adaptive card decoration missing border radius');
        return false;
      }

      // Validate platform-specific styling
      if (PlatformAdaptations.isIOS) {
        // iOS should have flat button style
        final elevationValue = buttonStyle.elevation?.resolve({});
        if (elevationValue != null && elevationValue > 0) {
          AnxLog.warning('iOS buttons should have zero elevation');
          return false;
        }
      } else {
        // Android should have Material elevation
        final elevationValue = buttonStyle.elevation?.resolve({});
        if (elevationValue == null || elevationValue <= 0) {
          AnxLog.warning('Android buttons should have Material elevation');
          return false;
        }
      }

      AnxLog.info('Adaptive components validated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('Adaptive components validation failed: $e');
      return false;
    }
  }
}

/// Result of platform validation
class ValidationResult {
  final Map<String, bool> results;
  final List<String> errors;
  final bool overallSuccess;

  const ValidationResult({
    required this.results,
    required this.errors,
    required this.overallSuccess,
  });

  /// Returns a formatted report of the validation results
  String getReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== Platform UI Validation Report ===');
    buffer.writeln('Overall Success: ${overallSuccess ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln();

    buffer.writeln('Component Results:');
    results.forEach((component, success) {
      buffer.writeln('  ${success ? "✅" : "❌"} $component');
    });

    if (errors.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('Errors:');
      for (final error in errors) {
        buffer.writeln('  ❌ $error');
      }
    }

    return buffer.toString();
  }
}
